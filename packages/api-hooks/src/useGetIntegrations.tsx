import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";
import { Integrations } from "@/web/components/integrations-list";

export const useGetIntegrations = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["integrations"],
    queryFn: async () => {
      return await apiClient.get<Integrations>(`/workspace/integrations`);
    },
  });
};
