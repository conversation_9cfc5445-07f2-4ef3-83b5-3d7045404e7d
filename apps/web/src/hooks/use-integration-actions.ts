import { useApiClient } from "@/api-client";
import { useDeleteIntegration } from "@/api-hooks/useDeleteIntegration";
import { Integration } from "@/web/components/integrations-list";
import { getStrategy } from "@/web/integrations/registry";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

export function useIntegrationActions(integration: Integration) {
  const { apiClient } = useApiClient();
  const strategy = getStrategy(integration.id);
  const extraHook = strategy.useExtraData?.() ?? { data: undefined, isLoading: false };

  const queryClient = useQueryClient();

  const {
    mutate: connect,
    isPending: isConnecting,
  } = useMutation({
    mutationFn: () =>
      strategy.connect({
        apiClient,
        extra: extraHook.data,
        integration,
      }),
    onSuccess: () => {
      toast.success("Connected successfully!");
      queryClient.invalidateQueries({ queryKey: ["integrations"] });
    },
    onError: () => toast.error("Failed to connect. Please try again."),
  });


  const {
    mutate: genericDisconnect,
    isPending: isDisconnecting,
  } = useDeleteIntegration();

  const disconnect = () => {
    if (strategy.disconnect) {
      return strategy
        .disconnect({ apiClient, integration })
        .then(() => {
          toast.success("Disconnected successfully!");
          queryClient.invalidateQueries({ queryKey: ["integrations"] });
        })
        .catch(() =>
          toast.error("Failed to disconnect. Please try again."),
        );
    }

    // fallback to generic endpoint
    genericDisconnect(integration.id, {
      onSuccess: () => {
        toast.success("Disconnected successfully!");
        queryClient.invalidateQueries({ queryKey: ["integrations"] });
      },
      onError: () =>
        toast.error("Failed to disconnect. Please try again."),
    });
  };

  return {
    connect,
    disconnect,
    isConnecting,
    isDisconnecting,
    extraLoading: extraHook.isLoading,
  };
}