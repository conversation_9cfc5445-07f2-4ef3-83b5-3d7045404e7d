"use client";

import { Unplug } from "lucide-react";
import Image from "next/image";
import * as React from "react";

import { useGetIntegrations } from "@/api-hooks/useGetIntegrations";
import { Button } from "@/web/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/web/components/ui/card";
import { useIntegrationActions } from "@/web/hooks/use-integration-actions";
import { Integration } from "@/web/integrations/types";

type IntegrationCardProps = {
  integration: Integration;
  isActive: boolean;
};

export function IntegrationsList() {
  const { data: integrations, isLoading } = useGetIntegrations();

  if (isLoading || !integrations) return <div>Loading...</div>;

  const { activeIntegrations, availableIntegrations } = integrations;

  return (
    <div className="space-y-8">
      <header className="space-y-1">
        <h3 className="text-lg font-semibold">Integrations</h3>
        <p className="text-muted-foreground text-sm">
          Connect your favourite tools and services to streamline your workflow.
        </p>
      </header>

      {activeIntegrations.length > 0 && (
        <Section
          title={`Active integrations (${activeIntegrations.length})`}
          integrations={activeIntegrations}
          isActive
        />
      )}

      {availableIntegrations.length > 0 && (
        <Section
          title={`Available integrations (${availableIntegrations.length})`}
          integrations={availableIntegrations}
          isActive={false}
        />
      )}
    </div>
  );
}

function Section({
  title,
  integrations,
  isActive,
}: {
  title: string;
  integrations: Integration[];
  isActive: boolean;
}) {
  return (
    <div className="space-y-4">
      <h3 className="text-base font-semibold">{title}</h3>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {integrations.map((integration) => (
          <IntegrationCard
            key={integration.id}
            integration={integration}
            isActive={isActive}
          />
        ))}
      </div>
    </div>
  );
}

function IntegrationCard({ integration, isActive }: IntegrationCardProps) {
  const { connect, disconnect, isConnecting, isDisconnecting, extraLoading } =
    useIntegrationActions(integration);

  return (
    <Card className="flex h-full flex-col">
      <CardHeader className="flex-1 pb-3">
        <div className="flex h-full items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-base">{integration.name}</CardTitle>
            <CardDescription className="mt-1">
              {integration.description}
            </CardDescription>
          </div>
          <div className="ml-3 flex h-8 w-8 flex-shrink-0 items-center justify-center overflow-hidden rounded-lg">
            <Image
              src={`/integrations/${integration.id}.png`}
              width={32}
              height={32}
              alt={`${integration.id} logo`}
              className="h-full w-full object-contain"
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="mt-auto pt-0">
        {isActive ? (
          <div className="flex gap-2">
            <Button variant="outline" className="flex-1">
              Active
            </Button>
            {integration.id !== "salesforce" ? (
              <Button
                variant="outline"
                size="icon"
                onClick={() => disconnect()}
                title="Disconnect"
                disabled={isDisconnecting}
              >
                <Unplug className="h-4 w-4" />
              </Button>
            ) : null}
          </div>
        ) : (
          <Button
            variant="default"
            className="w-full"
            onClick={() => connect()}
            disabled={extraLoading || isConnecting}
          >
            {extraLoading
              ? "Loading..."
              : isConnecting
                ? "Connecting..."
                : "Connect"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
