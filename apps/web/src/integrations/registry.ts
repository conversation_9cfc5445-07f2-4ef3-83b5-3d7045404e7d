import { defaultStrategy } from "@/web/integrations/default";
import { googleCalendarStrategy } from "@/web/integrations/googleCalendar";

import { IntegrationStrategy } from "@/web/integrations/types";

export const registry: Record<string, IntegrationStrategy<any>> = {
  [googleCalendarStrategy.id]: googleCalendarStrategy,
};

export function getStrategy(id: string): IntegrationStrategy<any> {
  return registry[id] ?? defaultStrategy;
}