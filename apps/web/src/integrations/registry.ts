import { defaultStrategy } from "@/web/integrations/default";
import { googleCalendarStrategy } from "@/web/integrations/googleCalendar";
import { IntegrationStrategy } from "@/web/integrations/types";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const registry: Record<string, IntegrationStrategy<any>> = {
  [googleCalendarStrategy.id]: googleCalendarStrategy,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getStrategy(id: string): IntegrationStrategy<any> {
  return registry[id] ?? defaultStrategy;
}