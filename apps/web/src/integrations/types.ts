import { ApiClient } from "@/api-client";

export interface IntegrationStrategy<Extra = unknown> {
  id: string;

  useExtraData?: () => {
    data: Extra | undefined;
    isLoading: boolean;
  };

  connect: (params: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: any;
  }) => Promise<void>;

  disconnect?: (params: {
    apiClient: ApiClient;
    integration: any;
  }) => Promise<void>;
}